@font-face {
  font-family: 'icomoon';
  src:  url('fonts/icomoon.eot?q8417j');
  src:  url('fonts/icomoon.eot?q8417j#iefix') format('embedded-opentype'),
    url('fonts/icomoon.ttf?q8417j') format('truetype'),
    url('fonts/icomoon.woff?q8417j') format('woff'),
    url('fonts/icomoon.svg?q8417j#icomoon') format('svg');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

[class^="icon-"], [class*=" icon-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'icomoon' !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-arrow-circle-down:before {
  content: "\e900";
}
.icon-arrow-circle-left:before {
  content: "\e901";
}
.icon-arrow-circle-right:before {
  content: "\e902";
}
.icon-arrow-circle-up:before {
  content: "\e903";
}
.icon-arrow-down:before {
  content: "\e904";
}
.icon-arrow-left:before {
  content: "\e905";
}
.icon-arrow-right:before {
  content: "\e906";
}
.icon-arrow-up:before {
  content: "\e907";
}
.icon-audio:before {
  content: "\e908";
}
.icon-book-reading:before {
  content: "\e909";
}
.icon-calendar:before {
  content: "\e90a";
}
.icon-coment:before {
  content: "\e90b";
}
.icon-facebook:before {
  content: "\e90c";
}
.icon-instagram:before {
  content: "\e90d";
}
.icon-linkedin:before {
  content: "\e90e";
}
.icon-play:before {
  content: "\e90f";
}
.icon-quotation-marks:before {
  content: "\e910";
}
.icon-x-twitter:before {
  content: "\e911";
}
.icon-youtube:before {
  content: "\e912";
}
